"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { useAuthContext } from "@/context/AuthContext";
import { toast } from "sonner";
import LoadingBars from "@/components/ui/loading-bars";
import { CheckCircle, XCircle } from "lucide-react";

interface MagicLinkVerifierProps {
  children: React.ReactNode;
}

/**
 * MagicLinkVerifier Component
 *
 * This component handles magic link verification when users click on magic links from their email.
 * It checks for 'token' and 'type=magiclink' URL parameters and automatically verifies the token
 * with the backend API endpoint: https://markket-be.onrender.com/api/v1/auth/magic-link/verify
 *
 * Flow:
 * 1. User clicks magic link from email (contains token and type parameters)
 * 2. User lands on root page with URL: /?token=xxx&type=magiclink
 * 3. This component detects the parameters and calls the verification API
 * 4. On success: User is authenticated and redirected to dashboard
 * 5. On error: User sees error message and URL parameters are cleaned up
 */

export default function MagicLinkVerifier({ children }: MagicLinkVerifierProps) {
  const searchParams = useSearchParams();
  const { verifyMagicLinkToken } = useAuthContext();
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<'idle' | 'success' | 'error'>('idle');

  useEffect(() => {
    const token = searchParams.get('token');
    const type = searchParams.get('type');

    // Check if this is a magic link verification request
    if (token && type === 'magiclink') {
      handleMagicLinkVerification(token);
    }
  }, [searchParams]);

  const handleMagicLinkVerification = async (token: string) => {
    setIsVerifying(true);
    setVerificationStatus('idle');

    try {
      await verifyMagicLinkToken(token);
      setVerificationStatus('success');

      toast.success("Welcome to Markket!", {
        description: "You have been successfully signed in.",
        duration: 4000,
      });

      // Clean up URL parameters
      const url = new URL(window.location.href);
      url.searchParams.delete('token');
      url.searchParams.delete('type');
      window.history.replaceState({}, '', url.toString());

      // Add a small delay before redirecting to show success message
      setTimeout(() => {
        // The redirect is handled in the verifyMagicLinkToken function
      }, 1500);

    } catch (error) {
      console.error("Magic link verification failed:", error);
      setVerificationStatus('error');

      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

      toast.error("Verification failed", {
        description: "The magic link is invalid or has expired. Please try signing in again.",
        duration: 6000,
      });

      // Clean up URL parameters even on error
      const url = new URL(window.location.href);
      url.searchParams.delete('token');
      url.searchParams.delete('type');
      window.history.replaceState({}, '', url.toString());

      // Redirect to home after showing error for a few seconds
      setTimeout(() => {
        setVerificationStatus('idle');
      }, 3000);
    } finally {
      setIsVerifying(false);
    }
  };

  // Show verification UI if currently verifying
  if (isVerifying) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <LoadingBars size="lg" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">
            Verifying your magic link...
          </h2>
          <p className="mt-2 text-gray-600">
            Please wait while we sign you in.
          </p>
        </div>
      </div>
    );
  }

  // Show success message briefly
  if (verificationStatus === 'success') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">
            Successfully signed in!
          </h2>
          <p className="mt-2 text-gray-600">
            Redirecting you to your dashboard...
          </p>
        </div>
      </div>
    );
  }

  // Show error message briefly
  if (verificationStatus === 'error') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <XCircle className="mx-auto h-16 w-16 text-red-500" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">
            Verification failed
          </h2>
          <p className="mt-2 text-gray-600">
            The magic link is invalid or has expired.
          </p>
          <p className="mt-1 text-sm text-gray-500">
            You will be redirected to the homepage shortly.
          </p>
        </div>
      </div>
    );
  }

  // Render normal content if not verifying
  return <>{children}</>;
}

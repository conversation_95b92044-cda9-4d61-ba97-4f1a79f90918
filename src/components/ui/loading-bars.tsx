import React from 'react';

interface LoadingBarsProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const LoadingBars: React.FC<LoadingBarsProps> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    xs: 'w-6 h-2',
    sm: 'w-8 h-2',
    md: 'w-10 h-3',
    lg: 'w-12 h-4',
    xl: 'w-16 h-5'
  };

  const barClasses = {
    xs: 'w-1 h-2',
    sm: 'w-1 h-2',
    md: 'w-1 h-3',
    lg: 'w-1.5 h-4',
    xl: 'w-2 h-5'
  };

  return (
    <div className={`inline-flex items-center justify-center gap-0.5 ${sizeClasses[size]} ${className}`}>
      <div className={`${barClasses[size]} bg-current rounded-sm animate-pulse`} style={{ animationDelay: '0ms', animationDuration: '1.4s' }}></div>
      <div className={`${barClasses[size]} bg-current rounded-sm animate-pulse`} style={{ animationDelay: '160ms', animationDuration: '1.4s' }}></div>
      <div className={`${barClasses[size]} bg-current rounded-sm animate-pulse`} style={{ animationDelay: '320ms', animationDuration: '1.4s' }}></div>
      <div className={`${barClasses[size]} bg-current rounded-sm animate-pulse`} style={{ animationDelay: '480ms', animationDuration: '1.4s' }}></div>
    </div>
  );
};

export default LoadingBars;

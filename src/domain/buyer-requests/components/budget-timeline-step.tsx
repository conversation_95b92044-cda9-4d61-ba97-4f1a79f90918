"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RequestFormData, BUDGET_RANGES, TIMELINE_OPTIONS } from "../types";
import {
  Calendar,
  ArrowRight,
  ArrowLeft,
  AlertTriangle,
  TrendingUp,
} from "lucide-react";

interface BudgetTimelineStepProps {
  data: Partial<RequestFormData>;
  onUpdate: (data: Partial<RequestFormData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

const CURRENCIES = [
  { code: "NG<PERSON>", symbol: "₦", name: "Nigerian Naira" },
  { code: "K<PERSON>", symbol: "KSh", name: "Kenyan Shilling" },
  { code: "G<PERSON>", symbol: "GH₵", name: "Ghanaian Cedi" },
  { code: "ZAR", symbol: "R", name: "South African Rand" },
];

export default function BudgetTimelineStep({
  data,
  onUpdate,
  onNext,
  onPrev,
}: BudgetTimelineStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validate = () => {
    const newErrors: Record<string, string> = {};

    if (!data.budgetType) {
      newErrors.budgetType = "Please select a budget type";
    }

    if (data.budgetType === "fixed" && !data.budgetMax) {
      newErrors.budgetMax = "Please enter your budget amount";
    }

    if (data.budgetType === "range") {
      if (!data.budgetMin) {
        newErrors.budgetMin = "Please enter minimum budget";
      }
      if (!data.budgetMax) {
        newErrors.budgetMax = "Please enter maximum budget";
      }
      if (
        data.budgetMin &&
        data.budgetMax &&
        data.budgetMin >= data.budgetMax
      ) {
        newErrors.budgetMax = "Maximum budget must be higher than minimum";
      }
    }

    if (!data.timeline) {
      newErrors.timeline = "Please select a timeline";
    }

    if (!data.currency) {
      newErrors.currency = "Please select a currency";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validate()) {
      onNext();
    }
  };

  const handleBudgetTypeChange = (
    budgetType: "fixed" | "range" | "negotiable"
  ) => {
    onUpdate({
      budgetType,
      budgetMin: budgetType === "negotiable" ? undefined : data.budgetMin,
      budgetMax: budgetType === "negotiable" ? undefined : data.budgetMax,
    });
  };

  const selectedCurrency =
    CURRENCIES.find(c => c.code === data.currency) || CURRENCIES[0];

  return (
    <div className="space-y-6 max-w-2xl min-w-2xl">
      <div className="grid gap-6">
        {/* Currency Selection */}
        {/* <Card>
          <CardHeader>
            <CardTitle className="text-lg">Currency</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="currency">Select Currency *</Label>
              <Select
                value={data.currency || "NGN"}
                onValueChange={value => onUpdate({ currency: value })}
              >
                <SelectTrigger
                  className={errors.currency ? "border-destructive" : ""}
                >
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  {CURRENCIES.map(currency => (
                    <SelectItem key={currency.code} value={currency.code}>
                      {currency.symbol} {currency.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.currency && (
                <p className="text-sm text-destructive">{errors.currency}</p>
              )}
            </div>
          </CardContent>
        </Card> */}

        {/* Budget Type */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <TrendingUp className="w-5 h-5 text-primary" />
              Budget Type
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <RadioGroup
                value={data.budgetType || ""}
                onValueChange={handleBudgetTypeChange}
                className="space-y-3"
              >
                <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-accent/50">
                  <RadioGroupItem value="fixed" id="fixed" />
                  <div className="flex-1">
                    <Label
                      htmlFor="fixed"
                      className="font-medium cursor-pointer"
                    >
                      Fixed Budget
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      I have a specific amount I want to spend
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-accent/50">
                  <RadioGroupItem value="range" id="range" />
                  <div className="flex-1">
                    <Label
                      htmlFor="range"
                      className="font-medium cursor-pointer"
                    >
                      Budget Range
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      I have a minimum and maximum budget
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-accent/50">
                  <RadioGroupItem value="negotiable" id="negotiable" />
                  <div className="flex-1">
                    <Label
                      htmlFor="negotiable"
                      className="font-medium cursor-pointer"
                    >
                      Negotiable
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {`I'm open to discussing prices with sellers`}
                    </p>
                  </div>
                </div>
              </RadioGroup>

              {errors.budgetType && (
                <p className="text-sm text-destructive">{errors.budgetType}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Budget Amount */}
        {(data.budgetType === "fixed" || data.budgetType === "range") && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Budget Amount</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.budgetType === "fixed" && (
                  <div className="space-y-2">
                    <Label htmlFor="budget">Budget Amount *</Label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                        {selectedCurrency.symbol}
                      </span>
                      <Input
                        id="budget"
                        type="number"
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                        value={data.budgetMax || ""}
                        onChange={e =>
                          onUpdate({
                            budgetMax: parseFloat(e.target.value) || 0,
                          })
                        }
                        className={`pl-8 ${errors.budgetMax ? "border-destructive" : ""}`}
                      />
                    </div>
                    {errors.budgetMax && (
                      <p className="text-sm text-destructive">
                        {errors.budgetMax}
                      </p>
                    )}
                  </div>
                )}

                {data.budgetType === "range" && (
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="budgetMin">Minimum Budget *</Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                          {selectedCurrency.symbol}
                        </span>
                        <Input
                          id="budgetMin"
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          value={data.budgetMin || ""}
                          onChange={e =>
                            onUpdate({
                              budgetMin: parseFloat(e.target.value) || 0,
                            })
                          }
                          className={`pl-8 ${errors.budgetMin ? "border-destructive" : ""}`}
                        />
                      </div>
                      {errors.budgetMin && (
                        <p className="text-sm text-destructive">
                          {errors.budgetMin}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="budgetMax">Maximum Budget *</Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                          {selectedCurrency.symbol}
                        </span>
                        <Input
                          id="budgetMax"
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          value={data.budgetMax || ""}
                          onChange={e =>
                            onUpdate({
                              budgetMax: parseFloat(e.target.value) || 0,
                            })
                          }
                          className={`pl-8 ${errors.budgetMax ? "border-destructive" : ""}`}
                        />
                      </div>
                      {errors.budgetMax && (
                        <p className="text-sm text-destructive">
                          {errors.budgetMax}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {/* Quick Budget Ranges */}
                <div className="space-y-2">
                  <Label className="text-sm">Quick Select (Optional)</Label>
                  <div className="flex flex-wrap gap-2">
                    {BUDGET_RANGES.map(range => (
                      <Button
                        key={range.label}
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          if (data.budgetType === "fixed") {
                            onUpdate({ budgetMax: range.max || range.min });
                          } else if (data.budgetType === "range") {
                            onUpdate({
                              budgetMin: range.min,
                              budgetMax: range.max || range.min * 2,
                            });
                          }
                        }}
                        className="text-xs"
                      >
                        {range.label}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Calendar className="w-5 h-5 text-primary" />
              Timeline
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>When do you need this? *</Label>
                <RadioGroup
                  value={data.timeline || ""}
                  onValueChange={value =>
                    onUpdate({
                      timeline: value as
                        | "urgent"
                        | "within_week"
                        | "within_month"
                        | "flexible",
                    })
                  }
                  className="space-y-2"
                >
                  {TIMELINE_OPTIONS.map(option => (
                    <div
                      key={option.value}
                      className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-accent/50"
                    >
                      <RadioGroupItem value={option.value} id={option.value} />
                      <div className="flex-1 flex items-center gap-2">
                        <span className="text-lg">{option.icon}</span>
                        <div>
                          <Label
                            htmlFor={option.value}
                            className="font-medium cursor-pointer"
                          >
                            {option.label}
                          </Label>
                        </div>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
                {errors.timeline && (
                  <p className="text-sm text-destructive">{errors.timeline}</p>
                )}
              </div>

              {/* Specific Date */}
              {data.timeline && data.timeline !== "flexible" && (
                <div className="space-y-2">
                  <Label htmlFor="deliveryDate">Specific Date (Optional)</Label>
                  <Input
                    id="deliveryDate"
                    type="date"
                    value={data.deliveryDate || ""}
                    onChange={e => onUpdate({ deliveryDate: e.target.value })}
                    min={new Date().toISOString().split("T")[0]}
                  />
                  <p className="text-xs text-muted-foreground">
                    {`Leave blank if you don't have a specific date in mind`}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6">
        <Button variant="outline" onClick={onPrev} className="gap-2">
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        <Button onClick={handleNext} className="gap-2">
          Continue to Location
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}

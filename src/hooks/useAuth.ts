import { useState, useCallback, useEffect } from "react";
import axios from "axios";
import { useRouter } from "next/navigation";
import type { RegisterPayload, User, UserRole } from "@/types/user";
import { registerUser, verifyMagicLink } from "@/lib/api/auth";

const API_BASE = "https://markket-be.onrender.com/api/v1";
const TOKEN_KEY = "markket_token";
const USER_KEY = "markket_user";

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const router = useRouter();

  // from ls
  useEffect(() => {
    const savedToken = localStorage.getItem(TOKEN_KEY);
    const savedUser = localStorage.getItem(USER_KEY);

    if (savedToken) {
      setToken(savedToken);
      if (savedUser) {
        setUser(JSON.parse(savedUser));
      }
    }
  }, []);

  const login = useCallback(
    async (email: string, password: string) => {
      try {
        const form = new URLSearchParams();
        form.append("grant_type", "password");
        form.append("username", email);
        form.append("password", password);
        form.append("scope", "");
        form.append("client_id", "");
        form.append("client_secret", "");

        const response = await axios.post(`${API_BASE}/auth/login`, form, {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        });

        const accessToken = response.data.access_token;
        setToken(accessToken);
        localStorage.setItem(TOKEN_KEY, accessToken);

        const firstName = email.split("@")[0];
        // const lastName = "User"; // Placeholder until we have a real last name
        // im using mock user until `/me` endpoint is ready
        const mockUser: User = {
          id: `user-${Date.now()}`,
          first_name: firstName,
          email,
          name: `${firstName} `,
          avatar_url: "",
          current_role: "buyer",
          roles: ["buyer", "seller", "rider"],
          is_new_user: false,
        };

        console.log("Mock user created:", mockUser);

        setUser(mockUser);
        localStorage.setItem(USER_KEY, JSON.stringify(mockUser));
        router.push("/dashboard");
      } catch (error) {
        console.error("Login failed:", error);
        throw error;
      }
    },
    [router]
  );

  const register = useCallback(
    async (payload: RegisterPayload) => {
      try {
        await registerUser(payload);
        await login(payload.email, payload.password);
      } catch (error) {
        console.error("Registration failed:", error);
        throw error;
      }
    },
    [login]
  );

  const logout = useCallback(() => {
    setUser(null);
    setToken(null);
    localStorage.removeItem(USER_KEY);
    localStorage.removeItem(TOKEN_KEY);
    router.push("/");
  }, [router]);

  const switchRole = useCallback(
    (role: UserRole) => {
      if (!user?.roles.includes(role)) return;
      const updatedUser = { ...user, current_role: role };
      setUser(updatedUser);
      localStorage.setItem(USER_KEY, JSON.stringify(updatedUser));
      router.push("/dashboard");
    },
    [user, router]
  );

  const verifyMagicLinkToken = useCallback(
    async (token: string) => {
      try {
        const response = await verifyMagicLink(token);

        // Handle different response structures
        const responseData = response.data;
        const access_token = responseData.access_token || responseData.token;
        const userData = responseData.user || responseData.data;

        if (!access_token) {
          throw new Error("No access token received from server");
        }

        // Set the access token
        setToken(access_token);
        localStorage.setItem(TOKEN_KEY, access_token);

        // If we have user data, set it; otherwise create a mock user
        if (userData) {
          setUser(userData);
          localStorage.setItem(USER_KEY, JSON.stringify(userData));
        } else {
          // Create a mock user if no user data is provided
          const mockUser: User = {
            id: `user-${Date.now()}`,
            first_name: "User",
            email: "<EMAIL>",
            name: "User",
            avatar_url: "",
            current_role: "buyer",
            roles: ["buyer", "seller", "rider"],
            is_new_user: false,
          };
          setUser(mockUser);
          localStorage.setItem(USER_KEY, JSON.stringify(mockUser));
        }

        // Redirect to dashboard
        router.push("/dashboard");

        return { success: true, user: userData };
      } catch (error) {
        console.error("Magic link verification failed:", error);

        // Provide more specific error messages
        if (axios.isAxiosError(error)) {
          const status = error.response?.status;
          const message = error.response?.data?.message || error.message;

          if (status === 400) {
            throw new Error("Invalid magic link token");
          } else if (status === 401) {
            throw new Error("Magic link has expired");
          } else if (status === 404) {
            throw new Error("Magic link not found");
          } else {
            throw new Error(`Verification failed: ${message}`);
          }
        }

        throw error;
      }
    },
    [router]
  );

  return {
    user,
    isAuthenticated: !!user && !!token,
    login,
    logout,
    register,
    switchRole,
    verifyMagicLinkToken,
    token,
  };
}
